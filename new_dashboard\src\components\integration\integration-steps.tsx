import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, CheckCircle } from 'lucide-react';
import { Button } from '../ui/button';
import './integration-steps.scss';

interface IntegrationStepsProps {
  steps: string[];
  title: string;
}

const IntegrationSteps: React.FC<IntegrationStepsProps> = ({ steps, title }) => {
  const navigate = useNavigate();

  const handleGoBack = () => {
    navigate(-1);
  };

  const processStepContent = (step: string): JSX.Element => {
    // Handle links in the format <text:url>
    const linkRegex = /<([^:]+):([^>]+)>/g;
    const processedStep = step.replace(
      linkRegex,
      (_, text, url) => `<a href="${url}" target="_blank" rel="noopener noreferrer">${text}</a>`
    );

    return (
      <div
        dangerouslySetInnerHTML={{
          __html: processedStep.replace(/\n/g, '<br />'),
        }}
      />
    );
  };

  return (
    <div className="integration-steps">
      {/* Header */}
      <div className="integration-steps__header">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleGoBack}
          className="integration-steps__back-btn"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <h3 className="integration-steps__title">Integration Steps</h3>
        <p className="integration-steps__subtitle">
          Follow these steps to connect your {title} account
        </p>
      </div>

      {/* Steps List */}
      <div className="integration-steps__list">
        {steps.map((step, index) => (
          <div key={index} className="integration-steps__item">
            {/* Step Indicator */}
            <div className="integration-steps__indicator">
              <div className="integration-steps__number">
                <CheckCircle className="w-4 h-4" />
              </div>
              {index < steps.length - 1 && (
                <div className="integration-steps__connector" />
              )}
            </div>

            {/* Step Content */}
            <div className="integration-steps__content">
              <div className="integration-steps__step-title">
                Step {index + 1}
              </div>
              <div className="integration-steps__step-description">
                {processStepContent(step)}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Footer Note */}
      <div className="integration-steps__footer">
        <div className="integration-steps__note">
          <div className="integration-steps__note-icon">💡</div>
          <p>
            Need help? Contact our support team if you encounter any issues during the integration process.
          </p>
        </div>
      </div>
    </div>
  );
};

export default IntegrationSteps;
