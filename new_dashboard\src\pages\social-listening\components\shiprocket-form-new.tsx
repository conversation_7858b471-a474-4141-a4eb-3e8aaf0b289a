import React from 'react';
import GenericIntegrationForm from '../../../components/integration/generic-integration-form';
import { connectDisconnectToShipRocket } from '../utils';

interface ShipRocketFormData {
  email: string;
  password: string;
}

const ShipRocketFormNew: React.FC = () => {
  const handleConnect = async (formData: any, clientId: string) => {
    const { email, password } = formData as ShipRocketFormData;
    
    await connectDisconnectToShipRocket({
      channel_name: 'shiprocket',
      client_id: clientId,
      email,
      password,
      isConnect: true,
    });
  };

  return (
    <GenericIntegrationForm
      integrationId="shiprocket"
      onConnect={handleConnect}
    />
  );
};

export default ShipRocketFormNew;
