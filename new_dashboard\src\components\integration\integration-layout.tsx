import React, { ReactNode } from 'react';
import { Card } from '../ui/card';
import './integration-layout.scss';

interface IntegrationLayoutProps {
  leftContent: ReactNode;
  logo: string;
  title: string;
  children: ReactNode;
}

const IntegrationLayout: React.FC<IntegrationLayoutProps> = ({
  leftContent,
  logo,
  title,
  children,
}) => {
  return (
    <div className="integration-layout">
      <div className="integration-layout__container">
        {/* Left Panel - Steps */}
        <div className="integration-layout__left">
          <Card className="integration-layout__left-card">
            {leftContent}
          </Card>
        </div>

        {/* Right Panel - Form */}
        <div className="integration-layout__right">
          <Card className="integration-layout__right-card">
            {/* Logo Header */}
            <div className="integration-layout__header">
              <div className="integration-layout__logo">
                <img src={logo} alt={`${title} logo`} />
              </div>
              <div className="integration-layout__title">
                <h2>{title} Integration</h2>
              </div>
            </div>

            {/* Form Content */}
            <div className="integration-layout__content">
              {children}
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default IntegrationLayout;
