import React from 'react';
import { <PERSON><PERSON> } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Alert, AlertDescription } from '../ui/alert';
import { Loader2, Shield, AlertCircle } from 'lucide-react';
import { IntegrationConfig } from './integration-configs';
import './integration-form.scss';

interface IntegrationFormProps {
  config: IntegrationConfig;
  formData: Record<string, string>;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  trying: boolean;
  apiError: { success: boolean; message: string } | null;
}

const IntegrationForm: React.FC<IntegrationFormProps> = ({
  config,
  formData,
  onChange,
  onSubmit,
  trying,
  apiError,
}) => {
  return (
    <div className="integration-form">
      <Card className="integration-form__card">
        <CardHeader className="integration-form__header">
          <div className="integration-form__header-icon">
            <Shield className="w-6 h-6" />
          </div>
          <CardTitle className="integration-form__title">
            Seller Panel
          </CardTitle>
          <CardDescription className="integration-form__description">
            {config.description}
          </CardDescription>
        </CardHeader>

        <CardContent className="integration-form__content">
          {/* Error Alert */}
          {apiError && !apiError.success && (
            <Alert variant="destructive" className="integration-form__error">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{apiError.message}</AlertDescription>
            </Alert>
          )}

          {/* Form */}
          <form onSubmit={onSubmit} className="integration-form__form">
            <div className="integration-form__fields">
              {config.fields.map((field) => (
                <div key={field.name} className="integration-form__field">
                  <Label 
                    htmlFor={field.name}
                    className="integration-form__label"
                  >
                    {field.label}
                    {field.required && (
                      <span className="integration-form__required">*</span>
                    )}
                  </Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    type={field.type}
                    placeholder={field.placeholder}
                    value={formData[field.name] || ''}
                    onChange={onChange}
                    required={field.required}
                    disabled={trying}
                    className="integration-form__input"
                  />
                </div>
              ))}
            </div>

            {/* Submit Button */}
            <div className="integration-form__actions">
              <Button
                type="submit"
                disabled={trying}
                className="integration-form__submit"
                size="lg"
              >
                {trying ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Connecting...
                  </>
                ) : (
                  'Connect'
                )}
              </Button>
            </div>
          </form>

          {/* Security Note */}
          <div className="integration-form__security">
            <div className="integration-form__security-icon">
              <Shield className="w-4 h-4" />
            </div>
            <p className="integration-form__security-text">
              Your credentials are encrypted and stored securely. We never share your information with third parties.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default IntegrationForm;
