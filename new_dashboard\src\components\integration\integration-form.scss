.integration-form {
  &__card {
    border: none;
    box-shadow: none;
    background: transparent;
  }

  &__header {
    text-align: center;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 2rem;
  }

  &__header-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    margin-bottom: 1rem;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }

  &__title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
  }

  &__description {
    color: #64748b;
    font-size: 0.875rem;
    line-height: 1.5;
    max-width: 400px;
    margin: 0 auto;
  }

  &__content {
    padding: 0;
  }

  &__error {
    margin-bottom: 1.5rem;
    border-color: #ef4444;
    background-color: #fef2f2;
    
    .alert-description {
      color: #dc2626;
    }
  }

  &__form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  &__fields {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  &__field {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  &__label {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  &__required {
    color: #ef4444;
    font-weight: 600;
  }

  &__input {
    height: 48px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: white;
    
    &:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      outline: none;
    }
    
    &:disabled {
      background-color: #f8fafc;
      color: #94a3b8;
      cursor: not-allowed;
    }
    
    &::placeholder {
      color: #9ca3af;
    }
  }

  &__actions {
    display: flex;
    justify-content: center;
    margin-top: 1rem;
  }

  &__submit {
    min-width: 200px;
    height: 48px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    
    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
    }
    
    &:active:not(:disabled) {
      transform: translateY(0);
    }
    
    &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
      transform: none;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
    }
  }

  &__security {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    margin-top: 2rem;
    padding: 1rem;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
  }

  &__security-icon {
    color: #10b981;
    flex-shrink: 0;
    margin-top: 0.125rem;
  }

  &__security-text {
    color: #64748b;
    font-size: 0.75rem;
    line-height: 1.5;
    margin: 0;
  }
}

// Responsive design
@media (max-width: 768px) {
  .integration-form {
    &__header {
      padding-bottom: 1rem;
      margin-bottom: 1.5rem;
    }

    &__header-icon {
      width: 40px;
      height: 40px;
    }

    &__title {
      font-size: 1.25rem;
    }

    &__description {
      font-size: 0.8125rem;
    }

    &__form {
      gap: 1.5rem;
    }

    &__fields {
      gap: 1.25rem;
    }

    &__input {
      height: 44px;
    }

    &__submit {
      min-width: 100%;
      height: 44px;
    }

    &__security {
      margin-top: 1.5rem;
      padding: 0.75rem;
    }

    &__security-text {
      font-size: 0.6875rem;
    }
  }
}
