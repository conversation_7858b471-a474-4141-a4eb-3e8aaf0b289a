import React from 'react';
import GenericIntegrationForm from '../../../components/integration/generic-integration-form';
import { connectDisconnectToUnicommerce } from '../utils';

interface UnicommerceFormData {
  username: string;
  password: string;
  tenantId: string;
  facility: string;
}

const UnicommerceFormNew: React.FC = () => {
  const handleConnect = async (formData: any, clientId: string) => {
    const { username, password, tenantId, facility } = formData as UnicommerceFormData;
    
    await connectDisconnectToUnicommerce({
      channel_name: 'unicommerce',
      client_id: clientId,
      username,
      password,
      tenant_id: tenantId,
      facility,
      isConnect: true,
    });
  };

  return (
    <GenericIntegrationForm
      integrationId="unicommerce"
      onConnect={handleConnect}
    />
  );
};

export default UnicommerceFormNew;
